import discord
from discord.ext import commands
from tools.checks import Perms
from tools.ext import Client, embed
from config.constants import Emojis, Colors


class VoiceMasterView(discord.ui.View):
    def __init__(self, bot):
        super().__init__(timeout=None)
        self.bot = bot
        self.client = Client()

    async def check_voice_ownership(self, interaction):
        """Check if user owns a voice channel"""
        if not interaction.user.voice:
            await interaction.response.send_message("❌ You're not in a voice channel!", ephemeral=True)
            return None
        
        channel = interaction.user.voice.channel
        
        # Check if user owns this temp channel
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT owner_id FROM temp_voice WHERE guild_id = %s AND channel_id = %s",
                        (interaction.guild.id, channel.id)
                    )
                    result = await cursor.fetchone()
            
            if not result or result[0] != interaction.user.id:
                await interaction.response.send_message("❌ You don't own this voice channel!", ephemeral=True)
                return None
            
            return channel
        except:
            await interaction.response.send_message("❌ Database error occurred!", ephemeral=True)
            return None

    @discord.ui.button(emoji=Emojis.lock, style=discord.ButtonStyle.grey, row=0)
    async def lock_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Lock voice channel button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return
        
        await channel.set_permissions(interaction.guild.default_role, connect=False)
        await interaction.response.send_message(f"{Emojis.lock} Voice channel locked!", ephemeral=True)
    
    @discord.ui.button(emoji=Emojis.unlock, style=discord.ButtonStyle.grey, row=0)
    async def unlock_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Unlock voice channel button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return
        
        await channel.set_permissions(interaction.guild.default_role, connect=True)
        await interaction.response.send_message(f"{Emojis.unlock} Voice channel unlocked!", ephemeral=True)
    
    @discord.ui.button(emoji=Emojis.ghost, style=discord.ButtonStyle.grey, row=0)
    async def ghost_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Ghost voice channel button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return
        
        await channel.set_permissions(interaction.guild.default_role, view_channel=False)
        await interaction.response.send_message(f"{Emojis.ghost} Voice channel ghosted!", ephemeral=True)
    
    @discord.ui.button(emoji=Emojis.unghost, style=discord.ButtonStyle.grey, row=0)
    async def reveal_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Reveal voice channel button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return
        
        await channel.set_permissions(interaction.guild.default_role, view_channel=True)
        await interaction.response.send_message(f"{Emojis.unghost} Voice channel revealed!", ephemeral=True)
    
    @discord.ui.button(emoji=Emojis.claim, style=discord.ButtonStyle.grey, row=0)
    async def claim_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Claim voice channel button"""
        if not interaction.user.voice:
            await interaction.response.send_message("❌ You're not in a voice channel!", ephemeral=True)
            return
        
        channel = interaction.user.voice.channel
        
        # Check if it's a temp channel and if owner is present
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT owner_id FROM temp_voice WHERE guild_id = %s AND channel_id = %s",
                        (interaction.guild.id, channel.id)
                    )
                    result = await cursor.fetchone()
            
            if not result:
                await interaction.response.send_message("❌ This is not a temporary voice channel!", ephemeral=True)
                return
            
            owner_id = result[0]
            owner = interaction.guild.get_member(owner_id)
            
            # Check if owner is still in the channel
            if owner and owner in channel.members:
                await interaction.response.send_message("❌ The channel owner is still present!", ephemeral=True)
                return
            
            # Transfer ownership
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "UPDATE temp_voice SET owner_id = %s WHERE guild_id = %s AND channel_id = %s",
                        (interaction.user.id, interaction.guild.id, channel.id)
                    )
            
            await interaction.response.send_message(f"{Emojis.claim} You now own this voice channel!", ephemeral=True)
            
        except Exception as e:
            await interaction.response.send_message("❌ Failed to claim channel!", ephemeral=True)

    @discord.ui.button(emoji=Emojis.disconnect, style=discord.ButtonStyle.grey, row=1)
    async def disconnect_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Disconnect member from voice channel button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return
        
        # Get all members in the channel except the owner
        members_in_channel = [member for member in channel.members if member.id != interaction.user.id]
        
        if not members_in_channel:
            await interaction.response.send_message("❌ No other members in your voice channel to disconnect!", ephemeral=True)
            return
        
        # Create member selection dropdown
        member_options = []
        for member in members_in_channel[:25]:  # Discord limit of 25 options
            member_options.append(discord.SelectOption(
                label=member.display_name,
                value=str(member.id),
                description=f"@{member.name}"
            ))
        
        # Add "Disconnect All" option if there are multiple members
        if len(members_in_channel) > 1:
            member_options.append(discord.SelectOption(
                label="🔌 Disconnect All Members",
                value="disconnect_all",
                description="Disconnect all members from the channel",
                emoji="🔌"
            ))
        
        class MemberSelectView(discord.ui.View):
            def __init__(self):
                super().__init__(timeout=60)
            
            @discord.ui.select(placeholder="Choose a member to disconnect...", options=member_options)
            async def member_select(self, select_interaction, select):
                try:
                    if select.values[0] == "disconnect_all":
                        # Disconnect all members except owner
                        disconnected_count = 0
                        for member in channel.members:
                            if member.id != interaction.user.id:
                                try:
                                    await member.move_to(None)
                                    disconnected_count += 1
                                except:
                                    pass
                        
                        await select_interaction.response.send_message(
                            f"{Emojis.disconnect} Disconnected **{disconnected_count}** members from {channel.mention}",
                            ephemeral=True
                        )
                    else:
                        member_id = int(select.values[0])
                        member_to_kick = interaction.guild.get_member(member_id)
                        
                        if not member_to_kick:
                            await select_interaction.response.send_message("❌ Member not found!", ephemeral=True)
                            return
                        
                        if member_to_kick not in channel.members:
                            await select_interaction.response.send_message("❌ That member is no longer in your voice channel!", ephemeral=True)
                            return
                        
                        # Disconnect the selected member
                        await member_to_kick.move_to(None)
                        
                        await select_interaction.response.send_message(
                            f"{Emojis.disconnect} **{member_to_kick.display_name}** has been disconnected from {channel.mention}",
                            ephemeral=True
                        )
                        
                except Exception as e:
                    await select_interaction.response.send_message(f"❌ Failed to disconnect member: {e}", ephemeral=True)
        
        view = MemberSelectView()
        await interaction.response.send_message("🔌 Select a member to disconnect:", view=view, ephemeral=True)

    @discord.ui.button(emoji=Emojis.activity, style=discord.ButtonStyle.grey, row=1)
    async def activity_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Start activity button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return
        
        # Create activity selection dropdown
        activities = [
            ("YouTube Together", "880218394199220334"),
            ("Poker Night", "755827207812677713"),
            ("Betrayal.io", "773336526917861400"),
            ("Fishington.io", "814288819477020702"),
            ("Chess In The Park", "832012774040141894"),
            ("Checkers In The Park", "832013003968348200"),
            ("Blazing 8s", "832025144389533716"),
            ("Land-io", "903769130790969345"),
            ("Putt Party", "945737671223947305"),
            ("Bobble League", "947957217959759964"),
            ("Ask Away", "976052223358406656"),
            ("Word Snacks", "879863976006127627")
        ]
        
        activity_options = []
        for name, app_id in activities[:10]:  # Discord limit of 25 options, we'll use 10
            activity_options.append(discord.SelectOption(label=name, value=app_id))
        
        class ActivitySelect(discord.ui.Select):
            def __init__(self):
                super().__init__(placeholder="Choose an activity...", options=activity_options)
            
            async def callback(self, select_interaction):
                try:
                    invite = await channel.create_invite(
                        target_type=discord.InviteTarget.embedded_application,
                        target_application_id=int(self.values[0]),
                        reason="Voice Master activity"
                    )
                    
                    activity_embed = discord.Embed(
                        title="🎮 Activity Started!",
                        description=f"[Click here to join the activity!]({invite.url})",
                        color=0x00ff00
                    )
                    
                    await select_interaction.response.send_message(embed=activity_embed, ephemeral=True)
                except Exception as e:
                    await select_interaction.response.send_message(f"❌ Failed to start activity: {e}", ephemeral=True)
        
        view = discord.ui.View()
        view.add_item(ActivitySelect())
        
        await interaction.response.send_message("🎮 Select an activity to start:", view=view, ephemeral=True)

    @discord.ui.button(emoji=Emojis.info, style=discord.ButtonStyle.grey, row=1)
    async def info_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Channel info button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return
        
        # Get channel info
        members_list = [member.display_name for member in channel.members]
        
        embed = discord.Embed(
            title=f"ℹ️ {channel.name}",
            color=Colors.default
        )
        embed.add_field(name="Owner", value=interaction.user.mention, inline=True)
        embed.add_field(name="Members", value=f"{len(channel.members)}/{channel.user_limit or '∞'}", inline=True)
        embed.add_field(name="Created", value=f"<t:{int(channel.created_at.timestamp())}:R>", inline=True)
        
        if members_list:
            embed.add_field(
                name=f"Members in Channel ({len(members_list)})",
                value="\n".join(members_list) if len(members_list) <= 10 else "\n".join(members_list[:10]) + f"\n... and {len(members_list) - 10} more",
                inline=False
            )
        
        await interaction.response.send_message(embed=embed, ephemeral=True)

    @discord.ui.button(emoji=Emojis.increase, style=discord.ButtonStyle.grey, row=1)
    async def increase_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Increase user limit button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return
        
        current_limit = channel.user_limit or 0
        if current_limit >= 99:
            await interaction.response.send_message("❌ User limit is already at maximum (99)!", ephemeral=True)
            return
        
        new_limit = current_limit + 1
        await channel.edit(user_limit=new_limit)
        
        await interaction.response.send_message(f"➕ User limit increased to {new_limit}!", ephemeral=True)

    @discord.ui.button(emoji=Emojis.decrease, style=discord.ButtonStyle.grey, row=1)
    async def decrease_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Decrease user limit button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return
        
        current_limit = channel.user_limit or 0
        if current_limit <= 1:
            await interaction.response.send_message("❌ User limit cannot be decreased below 1!", ephemeral=True)
            return
        
        new_limit = current_limit - 1 if current_limit > 0 else 0
        await channel.edit(user_limit=new_limit)
        
        await interaction.response.send_message(f"➖ User limit decreased to {new_limit if new_limit > 0 else 'No limit'}!", ephemeral=True)


class VoiceMaster(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.client = Client()

    async def check_vc_owner(self, ctx):
        """Check if user owns a voice channel"""
        if not ctx.author.voice:
            await embed.error(ctx, "You're not in a voice channel!")
            return None

        channel = ctx.author.voice.channel

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT owner_id FROM temp_voice WHERE guild_id = %s AND channel_id = %s",
                        (ctx.guild.id, channel.id)
                    )
                    result = await cursor.fetchone()

            if not result or result[0] != ctx.author.id:
                await embed.error(ctx, "You don't own this voice channel!")
                return None

            return channel
        except:
            await embed.error(ctx, "Database error occurred!")
            return None

    @commands.Cog.listener()
    async def on_voice_state_update(self, member, before, after):
        """Handle voice channel events"""
        # Check if user joined a voice master channel (the "create vc" channel)
        if after.channel:
            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "SELECT * FROM voicemaster WHERE guild_id = %s AND channel_id = %s",
                            (member.guild.id, after.channel.id)
                        )
                        vm_data = await cursor.fetchone()

                if vm_data:
                    # Create temporary voice channel
                    category = after.channel.category
                    temp_channel = await member.guild.create_voice_channel(
                        name=f"{member.display_name}'s Channel",
                        category=category,
                        user_limit=0  # No limit by default
                    )

                    # Move user to temp channel
                    await member.move_to(temp_channel)

                    # Store temp channel info
                    async with self.bot.db.acquire() as conn:
                        async with conn.cursor() as cursor:
                            await cursor.execute(
                                "INSERT INTO temp_voice (guild_id, channel_id, owner_id) VALUES (%s, %s, %s)",
                                (member.guild.id, temp_channel.id, member.id)
                            )

                    # Give VM role if configured
                    try:
                        async with self.bot.db.acquire() as conn:
                            async with conn.cursor() as cursor:
                                await cursor.execute(
                                    "SELECT vm_role_id FROM voicemaster_config WHERE guild_id = %s",
                                    (member.guild.id,)
                                )
                                role_data = await cursor.fetchone()

                        if role_data and role_data[0]:
                            role = member.guild.get_role(role_data[0])
                            if role and role not in member.roles:
                                await member.add_roles(role, reason="Voice Master user")
                    except:
                        pass

            except Exception as e:
                print(f"Voice Master creation error: {e}")

        # Check if user left a temp voice channel
        if before.channel:
            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "SELECT * FROM temp_voice WHERE guild_id = %s AND channel_id = %s",
                            (member.guild.id, before.channel.id)
                        )
                        temp_data = await cursor.fetchone()

                if temp_data and len(before.channel.members) == 0:
                    # Delete empty temp channel
                    await before.channel.delete(reason="Empty voice channel")
                    async with self.bot.db.acquire() as conn:
                        async with conn.cursor() as cursor:
                            await cursor.execute(
                                "DELETE FROM temp_voice WHERE channel_id = %s",
                                (before.channel.id,)
                            )

                # Remove VM role when leaving any voice channel
                if temp_data:
                    try:
                        async with self.bot.db.acquire() as conn:
                            async with conn.cursor() as cursor:
                                await cursor.execute(
                                    "SELECT vm_role_id FROM voicemaster_config WHERE guild_id = %s",
                                    (member.guild.id,)
                                )
                                role_data = await cursor.fetchone()

                        if role_data and role_data[0]:
                            role = member.guild.get_role(role_data[0])
                            if role and role in member.roles:
                                await member.remove_roles(role, reason="Left Voice Master channel")
                    except:
                        pass

            except Exception as e:
                print(f"Voice Master cleanup error: {e}")

    @commands.group(
        name="voicemaster",
        description="Voice Master system",
        aliases=["vm"],
        invoke_without_command=True
    )
    @Perms.get_perms("manage_channels")
    async def voicemaster(self, ctx):
        """Voice Master system"""
        if ctx.invoked_subcommand is None:
            # Invoke help for voicemaster command
            help_command = self.bot.get_command('help')
            if help_command:
                await ctx.invoke(help_command, command_name='voicemaster')
            else:
                # Fallback if help command not found
                await embed.default(ctx, "Use `vm setup` to create the VoiceMaster system or `vm help` for more commands")

    @voicemaster.command(
        name="setup",
        description="Setup voice master system"
    )
    @Perms.get_perms("manage_channels")
    async def vm_setup(self, ctx):
        """Setup voice master system"""
        try:
            # Send loading message
            message = await embed.loading(ctx, "Please wait while we setup VoiceMaster channels for you")

            # Create Voice Master category
            category = await ctx.guild.create_category(
                name="Voice Master",
                reason="Voice Master setup"
            )

            # Create panel text channel
            panel_channel = await ctx.guild.create_text_channel(
                name="panel",
                category=category,
                reason="Voice Master panel channel"
            )

            # Create "create vc" voice channel
            create_vc = await ctx.guild.create_voice_channel(
                name="create vc",
                category=category,
                reason="Voice Master create channel"
            )

            # Store in database
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "INSERT INTO voicemaster (guild_id, channel_id) VALUES (%s, %s) ON DUPLICATE KEY UPDATE channel_id = %s",
                        (ctx.guild.id, create_vc.id, create_vc.id)
                    )

            # Create the VoiceMaster panel embed with proper format
            panel_embed = discord.Embed(
                description="Use the buttons below to control your voice channel.",
                color=Colors.default
            )
            panel_embed.set_author(name="VoiceMaster Panel")
            panel_embed.set_thumbnail(url=self.bot.user.display_avatar.url)

            # Add button functions field with proper emojis and links
            button_functions = (
                f"{Emojis.lock} — [`Lock`](https://discord.gg/bleed) the voice channel\n"
                f"{Emojis.unlock} — [`Unlock`](https://dsc.gg/blurinv) the voice channel\n"
                f"{Emojis.ghost} — [`Ghost`](https://dsc.gg/blurinv) the voice channel\n"
                f"{Emojis.unghost} — [`Reveal`](https://dsc.gg/blurinv) the voice channel\n"
                f"{Emojis.claim} — [`Claim`](https://dsc.gg/blurinv) the voice channel\n"
                f"{Emojis.disconnect} — [`Disconnect`](https://dsc.gg/blurinv) a member\n"
                f"{Emojis.activity} — [`Start`](https://dsc.gg/blurinv) an activity\n"
                f"{Emojis.info} — [`View`](https://dsc.gg/blurinv) channel information\n"
                f"{Emojis.increase} — [`Increase`](https://dsc.gg/blurinv) the user limit\n"
                f"{Emojis.decrease} — [`Decrease`](https://dsc.gg/blurinv) the user limit"
            )

            panel_embed.add_field(
                name="**Button Functions**",
                value=button_functions,
                inline=True
            )

            # Create the view with buttons
            view = VoiceMasterView(self.bot)

            # Send the panel to the panel channel
            await panel_channel.send(embed=panel_embed, view=view)

            # Update with success message using embed utility
            await embed.success(ctx, "Finished setting up the VoiceMaster channels. A category and two channels have been created, you can move the channels or rename them if you want.")
            await message.delete()

        except Exception as e:
            await embed.error(ctx, f"Failed to setup Voice Master: {e}")

    @voicemaster.command(
        name="reset",
        description="Reset voice master system"
    )
    @Perms.get_perms("manage_channels")
    async def vm_reset(self, ctx):
        """Reset voice master system"""
        try:
            # Send loading message
            message = await embed.loading(ctx, "Please wait while we reset VoiceMaster system for you")

            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    # Get the voice master channel to find the category
                    await cursor.execute(
                        "SELECT channel_id FROM voicemaster WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    vm_data = await cursor.fetchone()

                    # Find and delete the Voice Master category and its channels
                    if vm_data:
                        vm_channel = self.bot.get_channel(vm_data[0])
                        if vm_channel and vm_channel.category:
                            category = vm_channel.category
                            # Delete all channels in the Voice Master category
                            for channel in category.channels:
                                try:
                                    await channel.delete(reason="Voice Master system reset")
                                except discord.NotFound:
                                    # Channel already deleted
                                    pass
                                except discord.Forbidden:
                                    # No permission to delete channel
                                    pass
                                except Exception:
                                    # Other errors
                                    pass
                            # Delete the category itself
                            try:
                                await category.delete(reason="Voice Master system reset")
                            except discord.NotFound:
                                # Category already deleted
                                pass
                            except discord.Forbidden:
                                # No permission to delete category
                                pass
                            except Exception:
                                # Other errors
                                pass

                    # Get all temp channels for this guild
                    await cursor.execute(
                        "SELECT channel_id FROM temp_voice WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    temp_channels = await cursor.fetchall()

                    # Delete temp channels
                    for temp_channel_data in temp_channels:
                        channel = self.bot.get_channel(temp_channel_data[0])
                        if channel:
                            try:
                                await channel.delete(reason="Voice Master system reset")
                            except discord.NotFound:
                                # Channel already deleted
                                pass
                            except discord.Forbidden:
                                # No permission to delete channel
                                pass
                            except Exception:
                                # Other errors
                                pass

                    # Clear database entries
                    await cursor.execute(
                        "DELETE FROM voicemaster WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    await cursor.execute(
                        "DELETE FROM temp_voice WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    await cursor.execute(
                        "DELETE FROM voicemaster_config WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )

            # Update with success message using embed utility
            await embed.success(ctx, "VoiceMaster system has been completely reset and all channels deleted")
            await message.delete()

        except Exception as e:
            await embed.error(ctx, f"Failed to reset Voice Master: {e}")

    @voicemaster.command(
        name="role",
        description="Set role for VoiceMaster users",
        usage="[role]"
    )
    @Perms.get_perms("manage_channels")
    async def vm_role(self, ctx, role: discord.Role):
        """Set VoiceMaster role"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "INSERT INTO voicemaster_config (guild_id, vm_role_id) VALUES (%s, %s) ON DUPLICATE KEY UPDATE vm_role_id = %s",
                        (ctx.guild.id, role.id, role.id)
                    )

            await embed.success(ctx, f"VoiceMaster role set to {role.mention}")

        except Exception as e:
            await embed.error(ctx, f"Failed to set VoiceMaster role: {e}")

    # Keep only essential commands - buttons handle most functionality
    @voicemaster.command(
        name="limit",
        description="Set user limit for your voice channel",
        usage="[number]"
    )
    async def vm_limit(self, ctx, limit: int):
        """Set voice channel limit"""
        channel = await self.check_vc_owner(ctx)
        if not channel:
            return

        if limit < 0 or limit > 99:
            await embed.warn(ctx, "Limit must be between 0-99!")
            return

        await channel.edit(user_limit=limit)
        await embed.success(ctx, f"User limit set to {limit if limit > 0 else 'No limit'}")

    @voicemaster.command(
        name="rename",
        description="Rename your voice channel",
        usage="[name]",
        aliases=["name"]
    )
    async def vm_rename(self, ctx, *, name: str):
        """Rename voice channel"""
        channel = await self.check_vc_owner(ctx)
        if not channel:
            return

        if len(name) > 100:
            await embed.warn(ctx, "Name too long! Maximum 100 characters.")
            return

        await channel.edit(name=name)
        await embed.success(ctx, f"Channel renamed to **{name}**")

    @voicemaster.command(
        name="permit",
        description="Allow a user to join your voice channel",
        usage="[user]"
    )
    async def vm_permit(self, ctx, member: discord.Member):
        """Permit user to join voice channel"""
        channel = await self.check_vc_owner(ctx)
        if not channel:
            return

        await channel.set_permissions(member, connect=True)
        await embed.success(ctx, f"{member.mention} can now join your voice channel")

    @voicemaster.command(
        name="reject",
        description="Deny a user access to your voice channel",
        usage="[user]"
    )
    async def vm_reject(self, ctx, member: discord.Member):
        """Reject user from voice channel"""
        channel = await self.check_vc_owner(ctx)
        if not channel:
            return

        await channel.set_permissions(member, connect=False)
        if member in channel.members:
            await member.move_to(None)

        await embed.success(ctx, f"{member.mention} has been denied access to your voice channel")

    @voicemaster.command(
        name="transfer",
        description="Transfer ownership of your voice channel",
        usage="[user]"
    )
    async def vm_transfer(self, ctx, member: discord.Member):
        """Transfer voice channel ownership"""
        channel = await self.check_vc_owner(ctx)
        if not channel:
            return

        if member not in channel.members:
            await embed.warn(ctx, "That user is not in your voice channel!")
            return

        if member.bot:
            await embed.warn(ctx, "You cannot transfer ownership to a bot!")
            return

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "UPDATE temp_voice SET owner_id = %s WHERE guild_id = %s AND channel_id = %s",
                        (member.id, ctx.guild.id, channel.id)
                    )

            await embed.success(ctx, f"Ownership transferred to {member.mention}")

        except Exception as e:
            await embed.error(ctx, f"Failed to transfer ownership: {e}")


async def setup(bot):
    await bot.add_cog(VoiceMaster(bot))
