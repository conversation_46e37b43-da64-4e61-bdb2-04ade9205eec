import discord
from discord.ext import commands
from tools.checks import Perms
from tools.ext import embed
from config.constants import Colors
from utils.parse import EmbedBuilder


class WelcomeConfig(commands.Cog):
    """Welcome message configuration system"""

    def __init__(self, bot):
        self.bot = bot

    @commands.group(
        name="welcome",
        description="Configure welcome messages",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_guild")
    async def welcome(self, ctx):
        """Configure welcome messages"""
        if ctx.invoked_subcommand is None:
            embed_obj = discord.Embed(
                title="👋 Welcome System",
                description="Configure welcome messages for new members",
                color=Colors.default
            )

            embed_obj.add_field(
                name="Commands",
                value="`welcome add [channel] [message]` - Add welcome message for channel\n"
                      "`welcome remove [channel]` - Remove welcome message for channel\n"
                      "`welcome view [channel]` - Test welcome message for channel\n"
                      "`welcome view` - Show all welcome channels\n"
                      "`welcome reset` - Delete all welcome messages\n"
                      "`welcome variables` - Show available variables",
                inline=False
            )

            embed_obj.add_field(
                name="Limits",
                value="• Maximum 3 welcome channels per server",
                inline=False
            )

            await ctx.reply(embed=embed_obj)

    @welcome.command(
        name="add",
        description="Add welcome message for channel",
        usage="[channel] [message]"
    )
    @Perms.get_perms("manage_guild")
    async def welcome_add(self, ctx, channel: discord.TextChannel, *, message: str):
        """Add welcome message for channel"""
        try:
            # Check current count
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT COUNT(*) FROM welcome_messages WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    count = (await cursor.fetchone())[0]

                    if count >= 3:
                        return await embed.warn(ctx, "Maximum 3 welcome channels allowed per server!")

                    # Check if channel already exists
                    await cursor.execute(
                        "SELECT * FROM welcome_messages WHERE guild_id = %s AND channel_id = %s",
                        (ctx.guild.id, channel.id)
                    )
                    existing = await cursor.fetchone()

                    if existing:
                        # Update existing message
                        await cursor.execute(
                            "UPDATE welcome_messages SET message = %s WHERE guild_id = %s AND channel_id = %s",
                            (message, ctx.guild.id, channel.id)
                        )
                        await embed.success(ctx, f"Welcome message updated for {channel.mention}")
                        return

                    # Add new welcome message
                    await cursor.execute(
                        "INSERT INTO welcome_messages (guild_id, channel_id, message) VALUES (%s, %s, %s)",
                        (ctx.guild.id, channel.id, message)
                    )

            await embed.success(ctx, f"Welcome message added for {channel.mention}")

        except Exception as e:
            await embed.error(ctx, f"Failed to add welcome message: {e}")

    @welcome.command(
        name="remove",
        description="Remove welcome message for channel",
        usage="[channel]"
    )
    @Perms.get_perms("manage_guild")
    async def welcome_remove(self, ctx, channel: discord.TextChannel):
        """Remove welcome message for channel"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "DELETE FROM welcome_messages WHERE guild_id = %s AND channel_id = %s",
                        (ctx.guild.id, channel.id)
                    )
                    affected_rows = cursor.rowcount

            if affected_rows == 0:
                return await embed.warn(ctx, f"No welcome message found for {channel.mention}")

            await embed.success(ctx, f"Welcome message removed for {channel.mention}")

        except Exception as e:
            await embed.error(ctx, f"Failed to remove welcome message: {e}")

    @welcome.command(
        name="view",
        description="View welcome messages",
        usage="<channel>"
    )
    @Perms.get_perms("manage_guild")
    async def welcome_view(self, ctx, channel: discord.TextChannel = None):
        """View welcome messages"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    if channel:
                        # Test specific channel
                        await cursor.execute(
                            "SELECT message FROM welcome_messages WHERE guild_id = %s AND channel_id = %s",
                            (ctx.guild.id, channel.id)
                        )
                        result = await cursor.fetchone()

                        if not result:
                            return await embed.warn(ctx, f"No welcome message found for {channel.mention}")

                        # Parse message with embed builder
                        message = result[0]

                        # Create a mock context for variable replacement
                        class MockContext:
                            def __init__(self, author, guild, channel):
                                self.author = author
                                self.guild = guild
                                self.channel = channel

                        mock_ctx = MockContext(ctx.author, ctx.guild, ctx.channel)
                        builder = EmbedBuilder(mock_ctx)

                        # Replace variables manually for welcome system
                        message = message.replace("{user}", ctx.author.mention)
                        message = message.replace("{user.mention}", ctx.author.mention)
                        message = message.replace("{user.name}", ctx.author.display_name)
                        message = message.replace("{user.id}", str(ctx.author.id))
                        message = message.replace("{server}", ctx.guild.name)
                        message = message.replace("{server.id}", str(ctx.guild.id))
                        message = message.replace("{count}", str(ctx.guild.member_count))

                        # Parse the message
                        parsed = builder.parse_embed_string(message)

                        # Send the parsed result
                        if parsed['embed']:
                            # Add test footer to embed
                            if not parsed['embed'].footer.text:
                                parsed['embed'].set_footer(text="🧪 This is a test message")
                            else:
                                parsed['embed'].set_footer(text=f"{parsed['embed'].footer.text} | 🧪 Test")

                            await channel.send(
                                content=parsed['content'],
                                embed=parsed['embed'],
                                view=parsed['view']
                            )
                        else:
                            # Simple message - create basic embed
                            embed_obj = discord.Embed(
                                description=parsed['content'] or message,
                                color=Colors.success
                            )
                            embed_obj.set_thumbnail(url=ctx.author.display_avatar.url)
                            embed_obj.set_footer(text="🧪 This is a test message")

                            await channel.send(embed=embed_obj, view=parsed['view'])
                        await embed.success(ctx, f"Test welcome message sent to {channel.mention}")

                    else:
                        # Show all welcome channels
                        await cursor.execute(
                            "SELECT channel_id FROM welcome_messages WHERE guild_id = %s",
                            (ctx.guild.id,)
                        )
                        results = await cursor.fetchall()

                        if not results:
                            return await embed.warn(ctx, "No welcome messages configured!")

                        channels = []
                        for result in results:
                            ch = ctx.guild.get_channel(result[0])
                            if ch:
                                channels.append(ch.mention)

                        if not channels:
                            return await embed.warn(ctx, "No valid welcome channels found!")

                        embed_obj = discord.Embed(
                            title="👋 Welcome Channels",
                            description="\n".join(f"• {ch}" for ch in channels),
                            color=Colors.default
                        )
                        await ctx.reply(embed=embed_obj)

        except Exception as e:
            await embed.error(ctx, f"Failed to view welcome messages: {e}")

    @welcome.command(
        name="reset",
        description="Delete all welcome messages"
    )
    @Perms.get_perms("manage_guild")
    async def welcome_reset(self, ctx):
        """Delete all welcome messages"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "DELETE FROM welcome_messages WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    affected_rows = cursor.rowcount

            if affected_rows == 0:
                return await embed.warn(ctx, "No welcome messages to delete!")

            await embed.success(ctx, f"Deleted {affected_rows} welcome message(s)")

        except Exception as e:
            await embed.error(ctx, f"Failed to reset welcome messages: {e}")

    @welcome.command(
        name="variables",
        description="Show available variables"
    )
    async def welcome_variables(self, ctx):
        """Show available variables"""
        embed_obj = discord.Embed(
            title="👋 Welcome Variables & Embed Guide",
            description="Available variables and embed syntax for welcome messages",
            color=Colors.default
        )

        embed_obj.add_field(
            name="User Variables",
            value="`{user}` - Mention the user\n"
                  "`{user.mention}` - Always mention\n"
                  "`{user.name}` - Display name\n"
                  "`{user.id}` - User ID\n"
                  "`{user.avatar}` - Avatar URL",
            inline=True
        )

        embed_obj.add_field(
            name="Server Variables",
            value="`{server}` - Server name\n"
                  "`{guild.name}` - Same as server\n"
                  "`{count}` - Member count\n"
                  "`{server.id}` - Server ID\n"
                  "`{guild.icon}` - Server icon URL",
            inline=True
        )

        embed_obj.add_field(
            name="Embed Syntax",
            value="`{embed}` - Start custom embed\n"
                  "`$v` - Separator between parts\n"
                  "`{title: text}` - Embed title\n"
                  "`{description: text}` - Description\n"
                  "`{color: name/hex}` - Color\n"
                  "`{field: name && value}` - Field",
            inline=False
        )

        embed_obj.add_field(
            name="Color Examples",
            value="**Named colors:** `red`, `blue`, `green`, `purple`, `orange`, `pink`, `yellow`, `cyan`, `magenta`, `lime`, `navy`, `teal`, `silver`, `gold`\n"
                  "**Hex colors:** `#ff0000`, `#00ff00`, `#0000ff`\n"
                  "**Discord colors:** `blurple`, `greyple`, `dark_theme`, `light_theme`",
            inline=False
        )

        embed_obj.add_field(
            name="Examples",
            value="**Simple:** `Welcome {user} to {server}!`\n"
                  "**Custom embed:** `{embed}$v{title: Welcome!}$v{description: Hey {user}!}$v{color: blue}`",
            inline=False
        )

        await ctx.reply(embed=embed_obj)


async def setup(bot):
    await bot.add_cog(WelcomeConfig(bot))
