import discord
import datetime
from discord.ext import commands
from tools.ext import embed
from config.constants import Colors
from cogs.old.auth import owners


class Whitelist(commands.Cog):
    """Server whitelist management commands"""
    
    def __init__(self, bot):
        self.bot = bot

    def is_bot_owner():
        """Check if user is a bot owner"""
        async def predicate(ctx: commands.Context):
            if ctx.author.id not in owners:
                await embed.error(ctx, "This command can only be used by **bot owners**")
                return False
            return True
        return commands.check(predicate)

    @commands.group(invoke_without_command=True)
    @is_bot_owner()
    async def whitelist(self, ctx):
        """Server whitelist management"""
        embed_obj = discord.Embed(
            title="🛡️ Server Whitelist",
            description="Manage server whitelist for the bot",
            color=Colors.default
        )

        embed_obj.add_field(
            name="Commands",
            value="`whitelist add [server_id]` - Add server to whitelist\n"
                  "`whitelist remove [server_id]` - Remove server from whitelist\n"
                  "`whitelist list` - List all whitelisted servers\n"
                  "`whitelist check [server_id]` - Check if server is whitelisted",
            inline=False
        )

        await ctx.reply(embed=embed_obj)

    @whitelist.command(name="add")
    @is_bot_owner()
    async def whitelist_add(self, ctx: commands.Context, guild_id: int):
        """Add a server to the whitelist"""
        try:
            # Validate guild ID format (Discord IDs are 17-19 digits)
            if len(str(guild_id)) < 17 or len(str(guild_id)) > 19:
                return await embed.error(ctx, f"Invalid server ID format: `{guild_id}`")

            # Check if already whitelisted
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM server_whitelist WHERE guild_id = %s", (guild_id,)
                    )
                    check = await cursor.fetchone()

            if check:
                return await embed.warn(ctx, f"Server `{guild_id}` is already whitelisted")

            # Add to whitelist
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "INSERT INTO server_whitelist (guild_id, added_at) VALUES (%s, %s)",
                        (guild_id, int(datetime.datetime.now().timestamp()))
                    )

            # Try to get server name
            guild = self.bot.get_guild(guild_id)
            guild_name = guild.name if guild else "Unknown Server"

            await embed.success(ctx, f"Added **{guild_name}** (`{guild_id}`) to the whitelist")

        except ValueError:
            await embed.error(ctx, f"Invalid server ID: `{guild_id}` must be a number")
        except Exception as e:
            await embed.error(ctx, f"Error adding server to whitelist: {e}")

    @whitelist.command(name="remove")
    @is_bot_owner()
    async def whitelist_remove(self, ctx: commands.Context, guild_id: int):
        """Remove a server from the whitelist"""
        try:
            # Check if whitelisted
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM server_whitelist WHERE guild_id = %s", (guild_id,)
                    )
                    check = await cursor.fetchone()

            if not check:
                return await embed.warn(ctx, f"Server `{guild_id}` is not whitelisted")

            # Remove from whitelist
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "DELETE FROM server_whitelist WHERE guild_id = %s", (guild_id,)
                    )

            # Try to get server name
            guild = self.bot.get_guild(guild_id)
            guild_name = guild.name if guild else "Unknown Server"

            await embed.success(ctx, f"Removed **{guild_name}** (`{guild_id}`) from the whitelist")

        except ValueError:
            await embed.error(ctx, f"Invalid server ID: `{guild_id}` must be a number")
        except Exception as e:
            await embed.error(ctx, f"Error removing server from whitelist: {e}")

    @whitelist.command(name="list")
    @is_bot_owner()
    async def whitelist_list(self, ctx):
        """List all whitelisted servers"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT * FROM server_whitelist ORDER BY added_at DESC")
                    results = await cursor.fetchall()

            if not results:
                return await embed.warn(ctx, "No servers are whitelisted")

            # Create paginated embeds
            embeds = []
            servers_per_page = 10
            
            for i in range(0, len(results), servers_per_page):
                page_results = results[i:i + servers_per_page]
                
                embed_obj = discord.Embed(
                    title=f"🛡️ Whitelisted Servers ({len(results)} total)",
                    color=Colors.default
                )
                
                description = ""
                for idx, (guild_id, added_at) in enumerate(page_results, start=i + 1):
                    guild = self.bot.get_guild(guild_id)
                    guild_name = guild.name if guild else "Unknown Server"
                    member_count = guild.member_count if guild else "Unknown"
                    
                    # Convert timestamp to readable date
                    date_added = datetime.datetime.fromtimestamp(added_at).strftime("%Y-%m-%d")
                    
                    description += f"`{idx}.` **{guild_name}** (`{guild_id}`)\n"
                    description += f"     Members: {member_count} | Added: {date_added}\n\n"
                
                embed_obj.description = description
                embed_obj.set_footer(text=f"Page {len(embeds) + 1} of {(len(results) - 1) // servers_per_page + 1}")
                embeds.append(embed_obj)

            if len(embeds) == 1:
                await ctx.reply(embed=embeds[0])
            else:
                await ctx.paginator(embeds)

        except Exception as e:
            await embed.error(ctx, f"Error fetching whitelist: {e}")

    @whitelist.command(name="check")
    @is_bot_owner()
    async def whitelist_check(self, ctx: commands.Context, guild_id: int):
        """Check if a server is whitelisted"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM server_whitelist WHERE guild_id = %s", (guild_id,)
                    )
                    check = await cursor.fetchone()

            guild = self.bot.get_guild(guild_id)
            guild_name = guild.name if guild else "Unknown Server"

            if check:
                _, added_at = check
                date_added = datetime.datetime.fromtimestamp(added_at).strftime("%Y-%m-%d %H:%M:%S")
                await embed.success(ctx, f"**{guild_name}** (`{guild_id}`) is whitelisted\nAdded: {date_added}")
            else:
                await embed.warn(ctx, f"**{guild_name}** (`{guild_id}`) is not whitelisted")

        except ValueError:
            await embed.error(ctx, f"Invalid server ID: `{guild_id}` must be a number")
        except Exception as e:
            await embed.error(ctx, f"Error checking whitelist: {e}")


async def setup(bot):
    await bot.add_cog(Whitelist(bot))
