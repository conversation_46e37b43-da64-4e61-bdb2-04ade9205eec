import discord, os, sys, asyncio, aiohttp, json
from discord.ext import commands
from typing import Union
from discord.ui import View, Button


class GoodRole(commands.Converter):
    async def convert(self, ctx: commands.Context, argument):
        try:
            role = await commands.RoleConverter().convert(ctx, argument)
        except commands.BadArgument:
            role = discord.utils.get(ctx.guild.roles, name=argument)
        if role is None:
            role = ctx.find_role(argument)
            if role is None:
                raise commands.BadArgument(f"No role called **{argument}** found")
        if role.position >= ctx.guild.me.top_role.position:
            raise commands.BadArgument("this role is above my top role")
        if ctx.author.id == ctx.guild.owner_id:
            return role
        if role.position >= ctx.author.top_role.position:
            raise commands.BadArgument(f"this role is above your top role")
        return role


class NoStaff(commands.Converter):
    async def convert(self, ctx: commands.Context, argument):
        try:
            member = await commands.MemberConverter().convert(ctx, argument)
        except commands.BadArgument:
            member = discord.utils.get(ctx.guild.members, name=argument)
        if member is None:
            raise commands.BadArgument(f"No member called **{argument}** found")
        if member.id == ctx.guild.me.id:
            raise commands.BadArgument("im invincible lol")
        if member.top_role.position >= ctx.guild.me.top_role.position:
            raise commands.BadArgument(f"**{member}** is above my top role")
        if ctx.author.id == ctx.guild.owner_id:
            return member
        if (
            member.top_role.position >= ctx.author.top_role.position
            or member.id == ctx.guild.owner_id
        ):
            raise commands.BadArgument(f"**{member}** is above your top role")
        return member


class Whitelist:
    async def whitelist_things(
        ctx: commands.Context,
        module: str,
        target: Union[discord.Member, discord.User, discord.TextChannel],
    ):
        check = await ctx.bot.db.fetchrow(
            "SELECT * FROM whitelist WHERE guild_id = $1 AND module = $2 AND object_id = $3 AND mode = $4",
            ctx.guild.id,
            module,
            target.id,
            "user"
            if isinstance(target, discord.Member) or isinstance(target, discord.User)
            else "channel",
        )
        if check:
            return await ctx.send_warning(
                f"{f'**{target}**' if isinstance(target, discord.Member) else target.mention} is **already** whitelisted for **{module}**"
            )
        await ctx.bot.db.execute(
            "INSERT INTO whitelist VALUES ($1,$2,$3,$4)",
            ctx.guild.id,
            module,
            target.id,
            "user"
            if isinstance(target, discord.Member) or isinstance(target, discord.User)
            else "channel",
        )
        return await ctx.send_success(
            f"{f'**{target}**' if isinstance(target, discord.Member) else target.mention} is now whitelisted for **{module}**"
        )

    async def unwhitelist_things(
        ctx: commands.Context,
        module: str,
        target: Union[discord.Member, discord.TextChannel],
    ):
        check = await ctx.bot.db.fetchrow(
            "SELECT * FROM whitelist WHERE guild_id = $1 AND module = $2 AND object_id = $3 AND mode = $4",
            ctx.guild.id,
            module,
            target.id,
            "user"
            if isinstance(target, discord.Member) or isinstance(target, discord.User)
            else "channel",
        )
        if not check:
            return await ctx.send_warning(
                f"{f'**{target}**' if isinstance(target, discord.Member) else target.mention} is **not** whitelisted for **{module}**"
            )
        await ctx.bot.db.execute(
            "DELETE FROM whitelist WHERE guild_id = $1 AND module = $2 AND object_id = $3 AND mode = $4",
            ctx.guild.id,
            module,
            target.id,
            "user"
            if isinstance(target, discord.Member) or isinstance(target, discord.User)
            else "channel",
        )
        return await ctx.send_success(
            f"{f'**{target}**' if isinstance(target, discord.Member) else target.mention} is no longer whitelisted for **{module}**"
        )


class Invoke:
    async def invoke_send(ctx: commands.Context, member: discord.Member, reason: str):
        check = await ctx.bot.db.fetchrow(
            "SELECT * FROM invoke WHERE guild_id = $1", ctx.guild.id
        )
        if check:
            channel = ctx.guild.get_channel(check["channel_id"])
            if channel:
                embed = discord.Embed(
                    color=ctx.bot.color,
                    title=f"{ctx.command.name} | {ctx.guild.name}",
                    timestamp=discord.utils.utcnow(),
                )
                embed.add_field(name="moderator", value=ctx.author, inline=True)
                embed.add_field(name="target", value=member, inline=True)
                embed.add_field(name="reason", value=reason, inline=True)
                embed.set_thumbnail(url=member.display_avatar.url)
                await channel.send(embed=embed)
                return True
        return False


class Paginator(View):
    def __init__(self, ctx: commands.Context, embeds: list):
        super().__init__(timeout=60)
        self.ctx = ctx
        self.embeds = embeds
        self.current = 0

    @discord.ui.button(emoji="<:left:1018156480991612999>", style=discord.ButtonStyle.blurple)
    async def previous(self, interaction: discord.Interaction, button: Button):
        if interaction.user.id != self.ctx.author.id:
            return await interaction.response.send_message("This is not your paginator", ephemeral=True)
        
        if self.current == 0:
            self.current = len(self.embeds) - 1
        else:
            self.current -= 1
        
        await interaction.response.edit_message(embed=self.embeds[self.current], view=self)

    @discord.ui.button(emoji="<:stop:1124999008142774303>", style=discord.ButtonStyle.red)
    async def stop(self, interaction: discord.Interaction, button: Button):
        if interaction.user.id != self.ctx.author.id:
            return await interaction.response.send_message("This is not your paginator", ephemeral=True)
        
        await interaction.response.edit_message(view=None)

    @discord.ui.button(emoji="<:right:1018156484170883154>", style=discord.ButtonStyle.blurple)
    async def next(self, interaction: discord.Interaction, button: Button):
        if interaction.user.id != self.ctx.author.id:
            return await interaction.response.send_message("This is not your paginator", ephemeral=True)
        
        if self.current == len(self.embeds) - 1:
            self.current = 0
        else:
            self.current += 1
        
        await interaction.response.edit_message(embed=self.embeds[self.current], view=self)

    async def on_timeout(self):
        for item in self.children:
            item.disabled = True
        try:
            await self.message.edit(view=self)
        except:
            pass


class EmbedBuilder:
    def __init__(self, **kwargs):
        self.embed = discord.Embed(**kwargs)

    def add_field(self, name, value, inline=True):
        self.embed.add_field(name=name, value=value, inline=inline)
        return self

    def set_author(self, name, icon_url=None, url=None):
        self.embed.set_author(name=name, icon_url=icon_url, url=url)
        return self

    def set_footer(self, text, icon_url=None):
        self.embed.set_footer(text=text, icon_url=icon_url)
        return self

    def set_image(self, url):
        self.embed.set_image(url=url)
        return self

    def set_thumbnail(self, url):
        self.embed.set_thumbnail(url=url)
        return self

    def build(self):
        return self.embed


class StartUp:
    async def startup(bot):
        print("🔄 Starting up bot components...")

    async def loadcogs(bot):
        """Auto-discover and load cogs from directory structure"""
        import os
        import importlib.util

        loaded_cogs = set()
        failed_cogs = []

        # Cogs to explicitly exclude (conflicts or deprecated)
        excluded_cogs = {
            "cogs.old.moderation.moderation",  # Replaced by new moderation system
            "cogs.old.snipes.events",          # Replaced by new snipe system
            "cogs.old.logs.logging",           # Replaced by new logging system
            "cogs.old.server.prefix",          # Replaced by fixed prefix
            "cogs.old.miscellaneous.owner",    # Replaced by fixed whitelist system - DUPLICATE LEAVE COMMAND
            "cogs.old.voicemaster.voicemaster", # Replaced by fixed voicemaster - DUPLICATE COG NAME
            "cogs.old.server.booster",         # Replaced by fixed booster - DUPLICATE COG NAME
            "cogs.old.miscellaneous.steal",    # Replaced by fixed steal - DUPLICATE COG NAME
            "cogs.old.guild",                  # Replaced by fixed guild - DUPLICATE LEAVE COMMAND
            "cogs.fixed.utility.prefix",       # Duplicate of utils.prefix - DUPLICATE PREFIX COG
            "cogs.old.auth",                   # No setup function
            "cogs.old.test",                   # Test cog
            "cogs.old.events"                  # Replaced by fixed events
        }

        # Priority loading order - these load first
        priority_cogs = [
            "cogs.fixed.utils.prefix",         # Load prefix system first
            "cogs.fixed.owner.whitelist",      # Load whitelist system
            "cogs.fixed.moderations.moderation", # Load new moderation system
            "cogs.fixed.snipe.snipe",          # Load new snipe system
        ]

        print("🔄 Auto-discovering cogs...")

        # Load priority cogs first
        print("📋 Loading priority cogs...")
        for cog_path in priority_cogs:
            if cog_path in excluded_cogs:
                continue

            try:
                await bot.load_extension(cog_path)
                loaded_cogs.add(cog_path)
                print(f"✅ Loaded {cog_path}")
            except Exception as e:
                failed_cogs.append((cog_path, str(e)))
                print(f"❌ Failed to load {cog_path}: {e}")

        # Auto-discover remaining cogs
        print("🔍 Discovering remaining cogs...")
        discovered_cogs = []

        # Scan cogs directory
        cogs_dir = "cogs"
        if os.path.exists(cogs_dir):
            for root, dirs, files in os.walk(cogs_dir):
                for file in files:
                    if file.endswith('.py') and not file.startswith('__'):
                        # Convert file path to module path
                        rel_path = os.path.relpath(os.path.join(root, file))
                        module_path = rel_path.replace(os.sep, '.').replace('.py', '')

                        # Skip if already loaded or excluded
                        if module_path in loaded_cogs or module_path in excluded_cogs:
                            continue

                        # Check if file has setup function
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                if 'async def setup(' in content or 'def setup(' in content:
                                    discovered_cogs.append(module_path)
                        except Exception:
                            continue

        # Load discovered cogs
        print(f"📦 Loading {len(discovered_cogs)} discovered cogs...")
        for cog_path in discovered_cogs:
            try:
                await bot.load_extension(cog_path)
                loaded_cogs.add(cog_path)
                print(f"✅ Loaded {cog_path}")
            except Exception as e:
                failed_cogs.append((cog_path, str(e)))
                print(f"❌ Failed to load {cog_path}: {e}")

        # Mark auto-loading as complete
        bot.cogs_loaded = True

        # Report summary
        print(f"\n📊 Cog Loading Summary:")
        print(f"✅ Successfully loaded: {len(loaded_cogs)} cogs")
        if failed_cogs:
            print(f"❌ Failed to load: {len(failed_cogs)} cogs")
            for cog, error in failed_cogs:
                if "already loaded" not in error.lower():
                    print(f"   - {cog}: {error}")
        print()

    async def load_from_folders(bot, folders):
        """Auto-load disabled to prevent conflicts - all cogs loaded manually"""
        if hasattr(bot, 'cogs_loaded') and bot.cogs_loaded:
            print("🚫 Auto-loading disabled - cogs already loaded manually")
            return

        print("⚠️  Auto-loading system disabled to prevent conflicts")
        print("📋 All cogs are now loaded manually through priority system")

    def identify(self):
        payload = {
            "op": self.IDENTIFY,
            "d": {
                "token": self.token,
                "properties": {
                    "$os": sys.platform,
                    "$browser": "Discord iOS",
                    "$device": "Discord iOS",
                },
                "compress": True,
                "large_threshold": 250,
                "shard": [self.shard_id, self.shard_count],
                "presence": {
                    "status": "online",
                    "since": 0,
                    "activities": [],
                    "afk": False,
                },
                "intents": self.intents.value,
            },
        }
        if self.session_id:
            payload["d"]["session_id"] = self.session_id
        if self.sequence:
            payload["d"]["seq"] = self.sequence
        self.send(payload)


async def create_db(bot):
    """Create database tables"""
    print("🗄️ Setting up database...")
    # Database setup would go here
    pass
